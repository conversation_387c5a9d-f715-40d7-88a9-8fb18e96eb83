"""
Management command to test SU Channel Manager webhook integration.

This command allows testing the webhook functionality by sending sample
reservation data to the webhook endpoint.
"""

import json
import requests
from django.core.management.base import BaseCommand, CommandError
from django.conf import settings
from apps.stay.models import Property


class Command(BaseCommand):
    help = 'Test SU Channel Manager webhook integration'

    def add_arguments(self, parser):
        parser.add_argument(
            '--url',
            type=str,
            default='http://localhost:8000/booking/webhook/su-reservations/',
            help='Webhook URL to test (default: localhost)'
        )
        parser.add_argument(
            '--property-id',
            type=str,
            help='Hotel ID of property to use in test (uses first onboarded property if not specified)'
        )
        parser.add_argument(
            '--reservation-id',
            type=str,
            default='TEST_RES_001',
            help='Reservation ID to use in test'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Print payload without sending request'
        )

    def handle(self, *args, **options):
        """Execute the command."""
        try:
            # Get property for testing
            property_id = options.get('property_id')
            if property_id:
                try:
                    property_obj = Property.objects.get(hotel_id=property_id)
                except Property.DoesNotExist:
                    raise CommandError(f'Property with hotel_id {property_id} not found')
            else:
                property_obj = Property.objects.filter(is_onboarded=True).first()
                if not property_obj:
                    raise CommandError('No onboarded properties found. Please create a property first.')

            self.stdout.write(f'Using property: {property_obj.name} (ID: {property_obj.hotel_id})')

            # Create test payload
            payload = self.create_test_payload(property_obj.hotel_id, options['reservation_id'])

            if options['dry_run']:
                self.stdout.write('\nTest payload:')
                self.stdout.write(json.dumps(payload, indent=2))
                return

            # Send request to webhook
            url = options['url']
            self.stdout.write(f'\nSending request to: {url}')

            try:
                response = requests.post(
                    url,
                    json=payload,
                    headers={'Content-Type': 'application/json'},
                    timeout=30
                )

                self.stdout.write(f'Response Status: {response.status_code}')
                self.stdout.write(f'Response Body: {response.text}')

                if response.status_code == 200:
                    self.stdout.write(
                        self.style.SUCCESS('✓ Webhook test successful!')
                    )
                else:
                    self.stdout.write(
                        self.style.ERROR(f'✗ Webhook test failed with status {response.status_code}')
                    )

            except requests.exceptions.RequestException as e:
                raise CommandError(f'Request failed: {str(e)}')

        except Exception as e:
            raise CommandError(f'Command failed: {str(e)}')

    def create_test_payload(self, hotel_id, reservation_id):
        """Create a test reservation payload."""
        return {
            "id": reservation_id,
            "hotel_id": hotel_id,
            "booked_at": "2024-01-15 10:30:00",
            "modified_at": "2024-01-15 10:30:00",
            "deposit": "100.00",
            "paymenttype": "Hotel Collect",
            "commissionamount": "25.00",
            "paymentdue": "200.00",
            "numberofinfants": "0",
            "numberofadults": "2",
            "numberofchildren": "0",
            "reservation_notif_id": f"NOTIF_{reservation_id}",
            "customer": {
                "email": "<EMAIL>",
                "first_name": "Test",
                "last_name": "Customer",
                "telephone": "+1234567890",
                "address": "123 Test Street",
                "city": "Test City",
                "state": "Test State",
                "countrycode": "US",
                "zip": "12345",
                "remarks": "Test reservation from webhook command"
            },
            "rooms": [
                {
                    "guest_name": "Test Customer",
                    "arrival_date": "2024-02-01",
                    "departure_date": "2024-02-05",
                    "totalprice": "500.00",
                    "totaltax": "50.00",
                    "numberofguests": "2",
                    "numberofadults": "2",
                    "numberofchildren": "0",
                    "roomstaystatus": "new",
                    "addons": {
                        "breakfast": "included",
                        "wifi": "free"
                    },
                    "taxes": {
                        "city_tax": "10.00",
                        "service_tax": "40.00"
                    }
                }
            ],
            "affiliation": {
                "hotel_id": hotel_id,
                "OTA_Code": "19"  # Booking.com
            }
        }
