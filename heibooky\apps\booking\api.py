from apps.stay.models import Property
from django.utils import timezone
from apps.booking.models import Booking, Reservation, Customer
from services.su_api import reservation_notification
import logging
from datetime import datetime
from services.notification.handlers import ReservationNotificationHandler
from django.db import transaction
from typing import Dict, Any, Optional, Tuple, List
from decimal import Decimal
from django.db.utils import IntegrityError

logger = logging.getLogger(__name__)

class ReservationChangeTracker:
    """Tracks changes in reservation details"""
    
    TRACKED_FIELDS = {
        'checkin_date': 'Check-in',
        'checkout_date': 'Check-out',
        'total_price': 'Total Price',
        'deposit': 'Deposit',
        'number_of_guests': 'Number of Guests',
        'number_of_adults': 'Number of Adults',
        'number_of_children': 'Number of Children',
        'number_of_infants': 'Number of Infants',
        'payment_type': 'Payment Type',
        'guest_name': 'Guest Name'
    }

    @staticmethod
    def detect_changes(old_reservation: Reservation, new_data: Dict[str, Any]) -> Dict[str, Tuple[Any, Any]]:
        """
        Detect changes between existing reservation and new data
        Returns dict of changed fields with their old and new values
        """
        changes = {}
        
        for field, display_name in ReservationChangeTracker.TRACKED_FIELDS.items():
            old_value = getattr(old_reservation, field)
            new_value = new_data.get(field)

            # Handle datetime fields
            if isinstance(old_value, datetime):
                new_value = to_aware_datetime(new_value)
            # Handle decimal fields
            elif isinstance(old_value, Decimal):
                new_value = Decimal(str(to_float(new_value, field)))
            
            if new_value is not None and old_value != new_value:
                changes[display_name] = (old_value, new_value)
                
        return changes

def validate_decimal(value: Any, field_name: str, max_value: float = 99999999.99) -> float:
    """Validate decimal values to prevent numeric field overflow."""
    try:
        float_val = float(value) if value not in (None, '') else 0.0
        if float_val > max_value:
            logger.warning(f"Value too large for {field_name}: {float_val}. Capping at {max_value}")
            return round(max_value, 2)
        return round(float_val, 2)
    except (ValueError, TypeError):
        logger.error(f"Invalid value for {field_name}: {value}")
        return 0.00

def to_float(value: Any, field_name: str = "unnamed field") -> float:
    """Convert a value to float with overflow protection."""
    return validate_decimal(value, field_name)

def to_int(value: Any) -> int:
    """Convert a value to integer, returning 0 if the value is None or empty."""
    return int(value) if value not in (None, '') else 0

def to_aware_datetime(value: Any) -> Optional[datetime]:
    """Convert a date string or naive datetime to a timezone-aware datetime."""
    if not value:
        return None
        
    if isinstance(value, str):
        try:
            # Try parsing datetime with time component first
            value = datetime.strptime(value, "%Y-%m-%d %H:%M:%S")
        except ValueError:
            try:
                # If that fails, try parsing just the date
                value = datetime.strptime(value, "%Y-%m-%d")
            except ValueError:
                logger.error(f"Invalid datetime format: {value}")
                return None
            
    if timezone.is_naive(value):
        return timezone.make_aware(value, timezone.get_current_timezone())
    return value


def process_push_reservation(reservations: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Process a single reservation from SU's push notification system.

    This function is designed to handle individual reservations pushed by SU's Channel Manager
    following their Push API method. It processes one reservation at a time and handles
    all the necessary database operations and notifications.

    Args:
        reservation_data: Dictionary containing reservation data from SU push notification

    Returns:
        Dictionary with processing status and details
    """
    logger.info(f"Processing push reservation: {reservations[0].get('id', 'unknown')}")

    try:
        properties = Property.objects.filter(is_onboarded=True).select_related().values_list('hotel_id', flat=True)

        if not properties:
            logger.warning("No onboarded properties found in database")
            return {"status": "warning", "message": "No onboarded properties found"}

        for hotel_id in properties:
            try:
                logger.info(f"Processing reservations for hotel {hotel_id}")

                if not reservations:
                    logger.info(f"No reservations found for property {hotel_id}")
                    continue
                
                affiliation = reservations[0]['affiliation']
                property = Property.objects.get(hotel_id=hotel_id)
                
                for reservation_data in reservations:
                    with transaction.atomic():
                        # Process customer data
                        customer_data = reservation_data.get("customer", {})
                        rooms = reservation_data.get("rooms", [])
                        
                        customer, _ = Customer.objects.update_or_create(
                            email=customer_data["email"],
                            defaults={
                                "first_name": customer_data.get("first_name"),
                                "last_name": customer_data.get("last_name"),
                                "telephone": customer_data.get("telephone"),
                                "address": customer_data.get("address"),
                                "city": customer_data.get("city"),
                                "state": customer_data.get("state"),
                                "country": customer_data.get("countrycode"),
                                "zip_code": customer_data.get("zip")
                            }
                        )

                        for room_data in rooms:
                            # Check for existing reservation to track changes
                            existing_reservation = Reservation.objects.filter(
                                id=reservation_data["id"]
                            ).first()

                            # Prepare reservation data
                            gross_price = to_float(room_data.get("totalprice"), "gross_price")
                            reservation_defaults = {
                                "guest_name": room_data.get("guest_name"),
                                "booked_at": to_aware_datetime(reservation_data.get("booked_at")),
                                "checkin_date": to_aware_datetime(room_data.get("arrival_date")),
                                "checkout_date": to_aware_datetime(room_data.get("departure_date")),
                                "gross_price": gross_price,
                                "total_price": round(gross_price * (0.396), 2),
                                "total_tax": to_float(room_data.get("totaltax"), "total_tax"),
                                "deposit": to_float(reservation_data.get("deposit"), "deposit"),
                                "commission_amount": to_float(reservation_data.get("commissionamount"), "commission_amount"),
                                "payment_due": to_float(reservation_data.get("paymentdue"), "payment_due"),
                                "cancellation_fee": to_float(reservation_data.get("cancellation_fee"), "cancellation_fee"),
                                "reservation_notif_id": reservation_data.get('reservation_notif_id'),
                                "payment_type": reservation_data.get("paymenttype"),
                                "remarks": customer_data.get("remarks"),
                                "addons": room_data.get("addons"),
                                "taxes": room_data.get("taxes"),
                                "number_of_infants": to_int(reservation_data.get("numberofinfants")),
                                "modified_at": to_aware_datetime(reservation_data.get("modified_at")),
                                "number_of_guests": to_int(room_data.get("numberofguests")),
                                "number_of_adults": to_int(room_data.get("numberofadults")),
                                "number_of_children": to_int(room_data.get("numberofchildren")),
                                "processed_at": to_aware_datetime(reservation_data.get("processed_at"))
                            }

                            # Track changes if reservation exists
                            changes = {}
                            if existing_reservation:
                                changes = ReservationChangeTracker.detect_changes(
                                    existing_reservation,
                                    reservation_defaults
                                )

                            # Update or create reservation
                            reservation, is_new = Reservation.objects.update_or_create(
                                id=reservation_data["id"],
                                defaults=reservation_defaults
                            )

                            # Process booking with duplicate handling
                            try:
                                with transaction.atomic():
                                    # Try to get existing booking first
                                    existing_booking = Booking.objects.filter(
                                        reservation_data__id=reservation_data["id"]
                                    ).select_related('property', 'customer').first()

                                    status = room_data.get("roomstaystatus")
                                    booking_defaults = {
                                        "property": property,
                                        "customer": customer,
                                        "is_manual": False,
                                        "status": status,
                                        "checkin_date": to_aware_datetime(room_data.get("arrival_date")),
                                        "checkout_date": to_aware_datetime(room_data.get("departure_date")),
                                        "channel_code": int(affiliation.get("OTA_Code")),
                                        "booking_date": to_aware_datetime(reservation_data.get("booked_at")),
                                    }

                                    if existing_booking:
                                        # Update existing booking
                                        for key, value in booking_defaults.items():
                                            setattr(existing_booking, key, value)
                                        existing_booking.save()
                                        booking = existing_booking
                                        is_new_booking = False
                                    else:
                                        # Create new booking
                                        booking = Booking.objects.create(
                                            reservation_data=reservation,
                                            **booking_defaults
                                        )
                                        is_new_booking = True

                                    # Handle notifications
                                    handler = ReservationNotificationHandler(property, customer)
                                    
                                    if is_new_booking or status == Booking.Status.NEW or status == Booking.Status.REQUEST:
                                        handler.new_reservation_handler(booking)
                                    elif changes and status == Booking.Status.MODIFIED:
                                        handler.modified_reservation_handler(booking, changes)
                                    elif status == Booking.Status.CANCELLED:
                                        handler.cancelled_reservation_handler(booking)

                            except IntegrityError as e:
                                logger.error(
                                    f"Integrity error processing booking for reservation {reservation_data['id']}: {str(e)}",
                                    exc_info=True
                                )
                                continue
                            except Exception as e:
                                logger.error(
                                    f"Error processing booking for reservation {reservation_data['id']}: {str(e)}",
                                    exc_info=True
                                )
                                continue

                            #acknowledge the reservation
                            reservation_notification(reservation)

            except Exception as e:
                logger.error(f"Error processing hotel {hotel_id}: {str(e)}")
                continue

        logger.info("Completed reservation fetch task successfully")
        return {"status": "success", "message": reservations[0]}
    
    except Exception as e:
        logger.error(f"Task error: {str(e)}")
        return {"status": "error", "message": str(e)}
