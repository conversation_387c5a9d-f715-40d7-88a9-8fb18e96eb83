from channels.generic.websocket import AsyncJsonWebsocketConsumer
from channels.db import database_sync_to_async
import logging
import os
from rest_framework_simplejwt.tokens import AccessToken
from rest_framework_simplejwt.exceptions import TokenError
from django.contrib.auth import get_user_model
from django.utils import timezone
from urllib.parse import parse_qs

logger = logging.getLogger(__name__)

class NotificationConsumer(AsyncJsonWebsocketConsumer):
    async def connect(self):
        self.user = self.scope["user"]
        logger.debug(f"Connection attempt by user: {self.user}")

        if not self.user.is_authenticated:
            logger.warning("Unauthenticated connection attempt")
            await self.close()
            return

        self.group_name = f"notifications_{self.user.id}"
        logger.debug(f"User authenticated, group name: {self.group_name}")

        await self.channel_layer.group_add(
            self.group_name,
            self.channel_name
        )
        await self.accept()
        logger.debug("Connection accepted")

    async def disconnect(self, close_code):
        if hasattr(self, 'group_name'):
            await self.channel_layer.group_discard(
                self.group_name,
                self.channel_name
            )

    async def receive_json(self, content, **kwargs):
        action = content.get("action")
        logger.debug(f"Received action: {action} from user {self.user.id}")

        if action == "mark_as_read":
            notification_ids = content.get("notification_ids", [])
            for notif_id in notification_ids:
                await self.mark_notification_as_read(notif_id)
        elif action == "ping":
            # Respond to ping with a pong to keep the connection alive
            logger.debug(f"Received ping from user {self.user.id}, sending pong")
            await self.send_json({"type": "pong"})

    async def mark_notification_as_read(self, notif_id):
        from apps.integrations.models import Notification
        try:
            notification = await database_sync_to_async(Notification.objects.get)(
                id=notif_id,
                user=self.user
            )
            if not notification.is_read:
                notification.is_read = True
                await database_sync_to_async(notification.save)()
                # Broadcast the update
                await self.channel_layer.group_send(
                    self.group_name,
                    {
                        "type": "notification_update",
                        "data": {
                            "id": str(notification.id),
                            "is_read": True
                        }
                    }
                )
        except Notification.DoesNotExist:
            pass

    async def notification_message(self, event):
        """Handle incoming notification messages"""
        await self.send_json(event["message"])

    async def send_notification(self, event):
        """Handle send_notification message type"""
        await self.send_json({
            "type": "notification",
            "message": event["data"]
        })

    async def notification_update(self, event):
        """Handle notification update messages"""
        await self.send_json({
            "type": "notification_update",
            "data": event["data"]
        })

class SupportChatConsumer(AsyncJsonWebsocketConsumer):
    async def connect(self):
        # Get query parameters from the scope
        query_string = self.scope.get('query_string', b'').decode()
        query_params = parse_qs(query_string)
        
        # Extract token from query parameters
        token = query_params.get('token', [None])[0]
        logger.debug(f"Support chat connection attempt with token: {token is not None}")

        if not token:
            logger.warning("No authentication token provided in support chat connection")
            await self.close(code=4001)
            return

        # Authenticate the user using the token
        try:
            user = await self.get_user_from_token(token)
            if not user:
                logger.warning(f"Invalid token provided for support chat connection")
                await self.close(code=4002)
                return
            
            self.user = user
        except Exception as e:
            logger.error(f"Error authenticating token: {str(e)}")
            await self.close(code=4003)
            return

        # Get the chat_id from the URL path
        try:
            self.chat_id = self.scope["url_route"]["kwargs"].get("chat_id")
            if not self.chat_id:
                logger.warning("No chat_id provided in URL path")
                await self.close(code=4004)
                return

            # Validate the chat_id
            chat = await self.get_chat(self.chat_id)
            if not chat:
                logger.warning(f"Chat with ID {self.chat_id} not found")
                await self.close(code=4005)
                return
                
            self.target_user_id = str(chat['user_id'])
            
            # Check permissions
            if not self.user.is_staff and str(self.user.id) != self.target_user_id:
                logger.warning(f"User {self.user.id} attempted to access another user's chat {self.chat_id}")
                await self.close(code=4006)
                return
                
        except Exception as e:
            logger.error(f"Error getting chat details: {str(e)}")
            await self.close(code=4007)
            return

        # Create a unique group name for this chat
        self.group_name = f"support_chat_{self.target_user_id}"
        logger.debug(f"User connecting to support chat, group name: {self.group_name}")

        # Join the chat group
        await self.channel_layer.group_add(
            self.group_name,
            self.channel_name
        )
        await self.accept()
        logger.debug(f"Support chat connection accepted for chat {self.chat_id}")

    @database_sync_to_async
    def get_user_from_token(self, token):
        """
        Get user from JWT token
        """
        User = get_user_model()
        try:
            # Verify the token and get the user ID
            access_token = AccessToken(token)
            user_id = access_token.get('user_id')
            
            if not user_id:
                logger.warning("Token does not contain user_id")
                return None
            
            # Fetch the user using the ID from the token
            user = User.objects.get(id=user_id)
            return user
        except TokenError as e:
            logger.warning(f"Invalid token: {str(e)}")
            return None
        except User.DoesNotExist:
            logger.warning(f"User from token not found")
            return None
        except Exception as e:
            logger.error(f"Unexpected error authenticating token: {str(e)}")
            raise
    
    @database_sync_to_async
    def get_chat(self, chat_id):
        from apps.support.models import Chat
        try:
            chat = Chat.objects.filter(id=chat_id).values('id', 'user_id', 'status', 'priority').first()
            return chat
        except Exception:
            return None

    async def disconnect(self, close_code):
        if hasattr(self, 'group_name'):
            await self.channel_layer.group_discard(
                self.group_name,
                self.channel_name
            )
            logger.debug(f"Support chat disconnected from {self.group_name}")

        async def receive_json(self, content, **kwargs):
            """Handle incoming messages from WebSocket"""
            action = content.get("type")
            logger.debug(f"Received support chat action: {action} from user {self.user.id}")

            if action == "chat.message":
                try:
                    message_text = content.get("message")
                    if not message_text:
                        return

                    # Save the message to the database
                    message = await self.save_support_message(
                        self.chat_id,
                        message_text,
                        is_from_support=self.user.is_staff
                    )
                    
                    # Broadcast the message to the group
                    await self.channel_layer.group_send(
                        self.group_name,
                        {
                            "type": "chat_message",
                            "message": message["message"],
                            "sender": "support" if message["is_from_support"] else "user",
                            "timestamp": message["created_at"],
                            "id": message["id"],
                            "status": message["chat_status"],
                            "priority": message["chat_priority"]
                        }
                    )
                except Exception as e:
                    logger.error(f"Error handling chat message: {str(e)}")
                    await self.send_json({"error": "Failed to send chat message"})
            elif action == "typing.start":
                # Handle typing indicator start
                await self.handle_typing_start()
            
            elif action == "typing.stop":
                # Handle typing indicator stop
                await self.handle_typing_stop()
                
            elif action == "file.upload.notify":
                # Handle file upload notification
                await self.handle_file_upload_notification(content)
                    
            elif action == "ping":
                await self.send_json({"type": "pong"})
        async def chat_message(self, event):
            """Handle chat message event and send to WebSocket"""
            message_data = {
                "type": "chat.message",
                "message": event["message"],
                "sender": event["sender"],
                "timestamp": event["timestamp"],
                "id": event["id"],
                "status": event["status"],
                "priority": event["priority"]
            }
            
            # Include attachments if available
            if "attachments" in event:
                message_data["attachments"] = event["attachments"]
                
            await self.send_json(message_data)

    @database_sync_to_async
    def save_support_message(self, chat_id, message, is_from_support):
        from apps.support.models import Chat, SupportMessage
        
        try:
            # Get the chat
            chat = Chat.objects.get(id=chat_id)
            
            # Update chat status based on the message sender
            if is_from_support:
                if chat.status in ['pending', 'resolved']:
                    chat.status = 'in_progress'
            else:
                if chat.status == 'resolved':
                    chat.status = 'pending'
                    
            chat.save(update_fields=['status', 'updated_at', 'last_message_at'])
            
            # Create the message
            support_message = SupportMessage.objects.create(
                chat=chat,
                message=message,
                is_from_support=is_from_support
            )
            
            # Return the message details
            return {
                "id": str(support_message.id),
                "message": support_message.message,
                "is_from_support": support_message.is_from_support,
                "created_at": support_message.created_at.isoformat(),
                "chat_status": chat.status,
                "chat_priority": chat.priority
            }
        except Exception as e:
            logger.error(f"Error saving support message: {str(e)}")
            raise

    async def handle_typing_start(self):
        """Handle typing start event"""
        try:
            await self.channel_layer.group_send(
                self.group_name,
                {
                    "type": "typing_indicator",
                    "action": "start",
                    "user_id": str(self.user.id),
                    "user_name": self.user.name,
                    "is_support": self.user.is_staff,
                    "timestamp": timezone.now().isoformat()
                }
            )
        except Exception as e:
            logger.error(f"Error handling typing start: {str(e)}")

    async def handle_typing_stop(self):
        """Handle typing stop event"""
        try:
            await self.channel_layer.group_send(
                self.group_name,
                {
                    "type": "typing_indicator",
                    "action": "stop",
                    "user_id": str(self.user.id),
                    "user_name": self.user.name,
                    "is_support": self.user.is_staff,
                    "timestamp": timezone.now().isoformat()
                }
            )
        except Exception as e:
            logger.error(f"Error handling typing stop: {str(e)}")

    async def handle_file_upload_notification(self, content):
        """Handle file upload completion notification"""
        try:
            message_id = content.get("message_id")
            if message_id:
                # Get the message with attachments
                message_data = await self.get_message_with_attachments(message_id)
                if message_data:
                    await self.channel_layer.group_send(
                        self.group_name,
                        {
                            "type": "file_uploaded",
                            "message_id": message_id,
                            "attachments": message_data["attachments"],
                            "sender": "support" if self.user.is_staff else "user",
                            "timestamp": message_data["created_at"]
                        }
                    )
        except Exception as e:
            logger.error(f"Error handling file upload notification: {str(e)}")

    async def typing_indicator(self, event):
        """Send typing indicator to WebSocket"""
        # Don't send typing indicator to the sender
        if str(self.user.id) != event["user_id"]:
            await self.send_json({
                "type": "typing.indicator",
                "action": event["action"],
                "user_id": event["user_id"],
                "user_name": event["user_name"],
                "is_support": event["is_support"],
                "timestamp": event["timestamp"]
            })

    async def file_uploaded(self, event):
        """Send file upload notification to WebSocket"""
        await self.send_json({
            "type": "file.uploaded",
            "message_id": event["message_id"],
            "attachments": event["attachments"],
            "sender": event["sender"],
            "timestamp": event["timestamp"]
        })

    async def upload_progress(self, event):
        """Send upload progress notification to WebSocket"""
        await self.send_json({
            "type": "upload.progress",
            "message_id": event["message_id"],
            "progress": event["progress"],
            "sender": event["sender"]
        })

    @database_sync_to_async
    def get_message_with_attachments(self, message_id):
        """Get message with attachments for WebSocket notification"""
        try:
            from apps.support.models import SupportMessage
            message = SupportMessage.objects.select_related('chat').prefetch_related('attachments').get(id=message_id)
            
            attachments = []
            for attachment in message.attachments.all():
                attachments.append({
                    "id": str(attachment.id),
                    "file_name": attachment.file_name,
                    "file_size": attachment.file_size,
                    "content_type": attachment.content_type,
                    "file_url": attachment.file.url if attachment.file else None
                })
            
            return {
                "id": str(message.id),
                "message": message.message,
                "attachments": attachments,
                "created_at": message.created_at.isoformat()
            }
        except Exception as e:
            logger.error(f"Error getting message with attachments: {str(e)}")
            return None
