# Inventory Management System

## Overview

The Inventory Management System provides a robust solution for managing property availability through the SU API. This system handles inventory control for room bookings, blocking periods, and ensures that inventory is properly synchronized across all channels.

## Key Components

### 1. SU API Integration

The system integrates with the SU API through the `update_inventory` function in `services/su_api/su_api.py`. This function allows for:

- Setting room inventory to a specific value
- Supporting date ranges for inventory control
- Ensuring proper authentication and error handling
- Logging all API interactions

```python
@log_su_api_response("invratecontrol")
def update_inventory(property_instance, room_id, inventory_data, user=None) -> Dict[str, Any]:
    """
    Updates room inventory in the SU API.
    """
    payload = {
        "hotelid": property_instance.hotel_id,
        "room": [
            {
                "roomid": room_id,
                "date": inventory_data
            }
        ]
    }
    
    url = f"{settings.SU_API_BASE_URL}/invratecontrol"
    
    return send_request(
        "invratecontrol", 
        payload, 
        url=url, 
        user=user, 
        property_id=getattr(property_instance, 'id', None)
    )
```

### 2. Celery Tasks for Inventory Management

Three primary Celery tasks handle inventory updates:

#### a. `block_rooms_task`

Sets inventory to zero for a booking block's date range, effectively making the property unavailable for booking.

```python
@shared_task(bind=True, max_retries=3, default_retry_delay=300)
def block_rooms_task(self, block_id: int) -> Dict[str, Any]:
    """Task to block rooms by setting inventory to zero for the specified date range."""
    # Implementation details
```

#### b. `unblock_rooms_task`

Restores inventory to original quantities when a booking block is removed.

```python
@shared_task(bind=True, max_retries=3, default_retry_delay=300)
def unblock_rooms_task(self, property_id: str, start_date, end_date) -> Dict[str, Any]:
    """Task to unblock rooms by resetting inventory to the original quantity."""
    # Implementation details
```

#### c. `update_inventory_for_booking`

Updates inventory when bookings are created, modified, or cancelled.

```python
@shared_task(bind=True, max_retries=3, default_retry_delay=300)
def update_inventory_for_booking(self, booking_id: str, action: str = "decrease") -> Dict[str, Any]:
    """
    Task to update inventory based on booking creation, modification, or cancellation.
    """
    # Implementation details
```

### 3. Signal Handlers

The system uses Django signal handlers to automatically trigger inventory updates:

```python
@receiver(post_save, sender=BookingBlock)
def handle_booking_block_creation(sender, instance: BookingBlock, created: bool, **kwargs):
    """Handle booking block creation."""
    if created:
        transaction.on_commit(lambda: block_rooms_task.delay(instance.id))

@receiver(post_delete, sender=BookingBlock)
def handle_booking_block_deletion(sender, instance: BookingBlock, **kwargs):
    """Handle booking block deletion."""
    transaction.on_commit(lambda: unblock_rooms_task.delay(
        str(instance.property.id),
        instance.start_date,
        instance.end_date
    ))

@receiver(post_save, sender=Booking)
def handle_booking_inventory_update(sender, instance: Booking, created: bool, **kwargs):
    """Signal handler to update inventory when bookings are created, modified, or cancelled."""
    # Skip processing for manual bookings
    if instance.is_manual:
        return
        
    if created or instance.status in [Booking.Status.NEW, Booking.Status.MODIFIED]:
        # For new or modified bookings, decrease inventory
        transaction.on_commit(lambda: update_inventory_for_booking.delay(str(instance.id), "decrease"))
    elif instance.status == Booking.Status.CANCELLED:
        # For cancelled bookings, increase inventory
        transaction.on_commit(lambda: update_inventory_for_booking.delay(str(instance.id), "increase"))
```

### 4. Notification System

The system uses a unified `GeneralNotificationHandler` to ensure consistent notification delivery across:

- WebSocket notifications for real-time updates
- Database notifications for persistence
- Email notifications (when applicable)

```python
handler = GeneralNotificationHandler(
    users=user,
    title="Blocco prenotazioni creato con successo",
    message=message_text
)
handler.send_notification()
```

### 5. Action Logging System

The system uses the `SUAPIActionLog` model to persistently log all interactions with the SU API:

```python
SUAPIActionLog.objects.create(
    user=user,
    property_id=property_id,
    action=action,
    description=description,
    status=status,
    timestamp=timezone.now(),
    details=details or {}
)
```

This logging system provides:

- An audit trail of all API activities
- Debugging information for troubleshooting
- Performance monitoring data
- User activity tracking

The logs are automatically created via the `log_su_api_response` decorator applied to API functions.

## Workflow

### Booking Block Creation

1. User creates a booking block through the API
2. The model's `post_save` signal triggers `block_rooms_task`
3. `block_rooms_task` sends inventory update to SU API setting it to 0
4. Notifications are sent to user about successful block creation
5. The property becomes unavailable for booking during the blocked period

### Booking Block Removal

1. User deactivates a booking block through the API
2. The API view triggers the deletion of the block
3. The model's `post_delete` signal triggers `unblock_rooms_task`
4. `unblock_rooms_task` sends inventory update to SU API restoring original quantities
5. Notifications are sent to user about successful deactivation
6. The property becomes available for booking again

### Booking Creation/Modification

1. When a booking is created or modified, `handle_booking_inventory_update` signal handler runs
2. For new/modified bookings, it decreases inventory by 1
3. For cancelled bookings, it increases inventory by 1
4. All updates are processed asynchronously via Celery

## Error Handling

The system implements robust error handling with:

- Automatic retries for failed API requests using exponential backoff
- Comprehensive logging for all operations
- Transaction management to ensure data consistency
- Exception handling to prevent cascading failures

### Response Status Handling

The SU API returns responses with a success status that can be in different formats. The system properly checks for different success indicators:

```python
# Checking for both possible success response formats from SU API
is_successful = response.get("Success") == "Success" or response.get("Status") == "Success"

# Proper logging based on success/failure
if is_successful:
    logger.info(f"Successfully updated inventory: {response}")
else:
    logger.error(f"Failed to update inventory: {response}")
```

This ensures that successful API responses are correctly identified even when the API returns the status in different formats.

## Best Practices

- All inventory updates are performed asynchronously
- API calls are made only after database transactions are committed
- Events are logged for audit purposes
- Notifications are sent through a unified handler for consistency
